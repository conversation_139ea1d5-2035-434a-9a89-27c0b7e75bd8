import { defineConfig } from 'vite';
import fs from 'fs';
import path from 'path';

// 读取环境变量
function getProxyUrl() {
  try {
    const envPath = path.resolve(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf-8');
    const env = {};
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        env[key.trim()] = value.trim();
      }
    });
    return env.proxyUrl || 'localhost:8080';
  } catch (error) {
    console.warn('Failed to read .env file, using default proxy URL');
    return 'localhost:8080';
  }
}

export default defineConfig({
  root: './test',
  server: {
    port: 3000,
    open: true,
    proxy: {
      // HTTP 代理配置
      '^/(board|group|webapp|client|v1|user|workflow|integration|mepx/v1|ws|w)': {
        target: `https://${getProxyUrl()}`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          // 动态更新目标地址
          proxy.on('proxyReq', (proxyReq, req, res) => {
            const proxyUrl = getProxyUrl();
            proxyReq.setHeader('host', proxyUrl);
            proxyReq.setHeader('Accept-Encoding', '');
          });
        }
      },
      // WebSocket 代理配置 - 单独配置 WebSocket
      '^/ws': {
        target: `wss://${getProxyUrl()}`,
        ws: true,
        changeOrigin: true,
        secure: true,
      }
    }
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  build: {
    outDir: '../dist',
    emptyOutDir: true,
  },
});
