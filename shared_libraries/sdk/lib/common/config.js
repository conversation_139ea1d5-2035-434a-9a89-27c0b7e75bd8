class SDKConfig {
    constructor(config) {
        Object.defineProperty(this, "_config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._config = config || {};
        // 返回一个代理实例而不是this
        return new Proxy(this, {
            get(target, prop, receiver) {
                // 如果访问的是私有属性或方法，直接返回
                if (prop in target || prop === '_config') {
                    return Reflect.get(target, prop, receiver);
                }
                // 否则从_config中获取
                return target._config[prop];
            },
            set(target, prop, value, receiver) {
                // 如果是私有属性或方法，直接设置
                if (prop in target || prop === '_config') {
                    return Reflect.set(target, prop, value, receiver);
                }
                // 否则设置到_config中
                // @ts-ignore
                target._config[prop] = value;
                return true;
            },
            has(target, prop) {
                // 检查属性是否存在
                return prop in target._config || prop in target;
            },
        });
    }
}
const sdkConfig = new SDKConfig({
    accessToken: '',
    useCookies: true,
    clientVersion: '',
    sessionId: '',
    contextPath: '/web',
});
export { sdkConfig };
export default sdkConfig;
