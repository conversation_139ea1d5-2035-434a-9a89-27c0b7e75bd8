import { ClientRequest, ClientRequestType, ClientParam, User, Board, Group, Contacts, ActionObject, Partner, WebApp, TelephonyDomain } from '@moxo/proto';
export declare function requestNode(type: ClientRequestType, params?: ClientParam[], user?: User | null, board?: Board | null, group?: Group | null, contacts?: Contacts | null): ClientRequest;
export declare function userRequestNode(type: ClientRequestType, user: User, params?: ClientParam[]): ClientRequest;
export declare function boardRequestNode(type: ClientRequestType, board: Board, params?: ClientParam[]): ClientRequest;
export declare function groupRequestNode(type: ClientRequestType, group: Group, params?: ClientParam[]): ClientRequest;
export declare function contactRequestNode(type: ClientRequestType, contacts: Contacts, params?: ClientParam[]): ClientRequest;
export declare function sessionRequestNode(type: ClientRequestType, session: ActionObject, params?: ClientParam[]): ClientRequest;
export declare function boardAndSessionRequestNode(type: ClientRequestType, board: Board, session: ActionObject, params?: ClientParam[]): ClientRequest;
export declare function partnerRequestNode(type: ClientRequestType, partner: Partner, params?: ClientParam[]): ClientRequest;
export declare function webappRequestNode(type: ClientRequestType, webapp: WebApp, params?: ClientParam[]): ClientRequest;
export declare function telephonyDomainRequestNode(type: ClientRequestType, domain: TelephonyDomain, params?: ClientParam[]): ClientRequest;
//# sourceMappingURL=requestNode.d.ts.map