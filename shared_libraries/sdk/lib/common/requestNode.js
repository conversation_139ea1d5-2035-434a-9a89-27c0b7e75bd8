export function requestNode(type, params, user, board, group, contacts) {
    let req = { type: type, object: {} };
    if (user) {
        req.object.user = user;
    }
    if (board) {
        req.object.board = board;
    }
    if (group) {
        req.object.group = group;
    }
    if (contacts) {
        req.object.contacts = contacts;
    }
    if (params) {
        req.params = params;
    }
    return req;
}
export function userRequestNode(type, user, params) {
    return requestNode(type, params, user, null, null);
}
export function boardRequestNode(type, board, params) {
    // if (board && board.id) {
    //   let mxBoard: MxBoard = getBoardById(board.id);
    //   if (!mxBoard) {
    //     // try to find instant board
    //     mxBoard = getInstantBoardById(board.id);
    //   }
    //
    //   if (mxBoard && mxBoard.option) {
    //     return boardRequestNodeWithOption(type, board, params, mxBoard.option);
    //   }
    // }
    return requestNode(type, params, null, board, null);
}
// export function boardRequestNodeWithOption(
//   type: ClientRequestType,
//   board: Board,
//   params?: ClientParam[],
//   option?: MxBoardOption,
// ): ClientRequest {
//   let viewAsGroup: Group = null;
//   if (option) {
//     if (option.userId) {
//       viewAsGroup = {
//         members: [
//           {
//             user: {
//               id: option.userId,
//             },
//           },
//         ],
//       };
//     }
//
//     if (option.viewToken) {
//       if (!params) {
//         params = [];
//       }
//
//       params.push({
//         name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
//         string_value: option.viewToken,
//       });
//     }
//
//     if (option.suppressFeed) {
//       if (!params) {
//         params = [];
//       }
//
//       let hasSuppressFeedParam: boolean =
//         params.filter((p) => p.name === ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED).length >
//         0
//           ? true
//           : false;
//       if (!hasSuppressFeedParam) {
//         params.push({
//           name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
//         });
//       }
//     }
//   }
//   return requestNode(type, params, null, board, viewAsGroup);
// }
export function groupRequestNode(type, group, params) {
    return requestNode(type, params, null, null, group);
}
export function contactRequestNode(type, contacts, params) {
    return requestNode(type, params, null, null, null, contacts);
}
export function sessionRequestNode(type, session, params) {
    let req = { type: type, object: { session: session } };
    if (params) {
        req.params = params;
    }
    return req;
}
export function boardAndSessionRequestNode(type, board, session, params) {
    let req = { type: type, object: {} };
    if (board) {
        req.object.board = board;
    }
    if (session) {
        req.object.session = session;
    }
    if (params) {
        req.params = params;
    }
    return req;
}
export function partnerRequestNode(type, partner, params) {
    let req = { type: type, object: {} };
    if (partner) {
        req.object.partner = partner;
    }
    if (params) {
        req.params = params;
    }
    return req;
}
export function webappRequestNode(type, webapp, params) {
    let req = { type: type, object: {} };
    if (webapp) {
        req.object.webapp = webapp;
    }
    if (params) {
        req.params = params;
    }
    return req;
}
export function telephonyDomainRequestNode(type, domain, params) {
    let req = { type: type, object: {} };
    if (domain) {
        req.object.telephony_domain = domain;
    }
    if (params) {
        req.params = params;
    }
    return req;
}
