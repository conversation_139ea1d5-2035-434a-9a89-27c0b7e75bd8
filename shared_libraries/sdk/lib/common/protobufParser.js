import * as protobuf from 'protobufjs';
import { ProtoBufDefineJSON } from '@moxo/proto/ProtoBufDefineJSON';
const root = protobuf.Root.fromJSON(ProtoBufDefineJSON);
const requestParser = root.lookup('ClientRequest');
const responseParser = root.lookup('ClientResponse');
export function encodeProtobuf(json) {
    let message = requestParser.fromObject(json);
    return requestParser.encode(message).finish();
}
export function decodeProtobuf(buffer) {
    let arrayBuf = new Uint8Array(buffer);
    return responseParser.toObject(responseParser.decode(arrayBuf), {
        enums: String,
        bytes: String,
        json: true,
    });
}
const ProtobufParser = {
    encode: encodeProtobuf,
    decode: decodeProtobuf,
};
export { ProtobufParser };
