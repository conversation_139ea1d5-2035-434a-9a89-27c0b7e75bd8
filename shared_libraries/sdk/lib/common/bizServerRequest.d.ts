import { ClientRequest, ClientResponse, ClientResponseCode } from '@moxo/proto';
import { IRequestParam, IRequestPromise } from '../network/ajax';
export declare const SUCCESS_RESPONSE_CODES: readonly [ClientResponseCode.RESPONSE_SUCCESS, ClientResponseCode.RESPONSE_ACCEPTED, ClientResponseCode.RESPONSE_NO_CONTENT, ClientResponseCode.RESPONSE_CONNECT_SUCCESS];
/**
 * 成功响应状态码类型
 */
export type SuccessResponseCode = (typeof SUCCESS_RESPONSE_CODES)[number];
export declare function isSuccessResponse(response: ClientResponse): boolean;
export declare function sendBizServerRequest<T>(body: ClientRequest, opts?: Partial<IRequestParam>, formater?: (response: ClientResponse) => T): IRequestPromise<T>;
//# sourceMappingURL=bizServerRequest.d.ts.map