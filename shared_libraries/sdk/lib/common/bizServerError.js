/**
 * Business server error class that extends the standard Error
 * Contains server response information for better error handling
 */
export class BizServerError extends Error {
    constructor(message, response, props) {
        super(message);
        Object.defineProperty(this, "code", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "detailCode", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "serverMessage", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "response", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.name = 'BizServerError';
        this.code = response.code;
        this.detailCode = response.detail_code;
        this.serverMessage = response.message;
        this.response = response;
        if (props) {
            Object.assign(this, props);
        }
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, BizServerError);
        }
    }
}
export function isBizServerError(error) {
    return (error &&
        typeof error === 'object' &&
        error.name === 'BizServerError' &&
        typeof error.message === 'string');
}
export function convertToBizServerError(response) {
    return new BizServerError(response.message || '', response);
}
export function buildServerError(message, type) {
    return new BizServerError(message, {
        code: type,
    });
}
