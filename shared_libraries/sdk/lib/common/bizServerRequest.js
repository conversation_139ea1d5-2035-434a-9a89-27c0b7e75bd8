import { ClientResponseCode } from '@moxo/proto';
import { AjaxContentType, AjaxMethod, RequestPromise, sendRequest, } from '../network/ajax';
import { convertToBizServerError } from './bizServerError';
export const SUCCESS_RESPONSE_CODES = [
    ClientResponseCode.RESPONSE_SUCCESS,
    ClientResponseCode.RESPONSE_ACCEPTED,
    ClientResponseCode.RESPONSE_NO_CONTENT,
    ClientResponseCode.RESPONSE_CONNECT_SUCCESS,
];
export function isSuccessResponse(response) {
    if (!response.code) {
        return true;
    }
    return SUCCESS_RESPONSE_CODES.includes(response.code);
}
export function sendBizServerRequest(body, opts, formater) {
    var _a, _b, _c;
    let requestUrl = window.location.origin;
    if ((_a = body.object) === null || _a === void 0 ? void 0 : _a.board) {
        requestUrl += '/board';
    }
    else if ((_b = body.object) === null || _b === void 0 ? void 0 : _b.user) {
        requestUrl += '/user';
    }
    else if ((_c = body.object) === null || _c === void 0 ? void 0 : _c.group) {
        requestUrl += '/group';
    }
    else {
        requestUrl += '/user';
    }
    if (!opts) {
        opts = {};
    }
    const opt = Object.assign({ method: AjaxMethod.POST }, opts);
    if (!opt.contentType) {
        opt.contentType = AjaxContentType.JSON;
    }
    const requestPromise = sendRequest(requestUrl, opt, body);
    return new RequestPromise((resolve, reject) => {
        requestPromise
            .then((response) => {
            const clientResponse = response;
            // 检查响应是否表示成功
            const isSuccess = isSuccessResponse(clientResponse);
            if (!isSuccess) {
                reject(convertToBizServerError(clientResponse));
            }
            else {
                if (formater) {
                    resolve(formater(clientResponse));
                }
                else {
                    resolve(clientResponse);
                }
            }
        })
            .catch((error) => {
            reject(convertToBizServerError(error));
        });
    }, requestPromise.requestId);
}
