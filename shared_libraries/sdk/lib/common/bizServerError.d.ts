import { ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';
/**
 * Business server error class that extends the standard Error
 * Contains server response information for better error handling
 */
export declare class BizServerError extends Error {
    readonly code?: ClientResponseCode | null;
    readonly detailCode?: ClientResponseDetailCode | null;
    readonly serverMessage?: string;
    readonly response?: ClientResponse | null;
    constructor(message: string, response: ClientResponse, props?: Record<string, boolean>);
}
export declare function isBizServerError(error: any): error is BizServerError;
export declare function convertToBizServerError(response: ClientResponse): BizServerError;
export declare function buildServerError(message: string, type: ClientResponseCode): BizServerError;
//# sourceMappingURL=bizServerError.d.ts.map