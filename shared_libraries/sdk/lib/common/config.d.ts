interface SDKConfigData {
    accessToken: string;
    useCookies: boolean;
    userId?: string;
    userToken?: string;
    sessionId: string;
    clientVersion: string;
    contextPath: string;
}
interface SDKConfig extends SDKConfigData {
}
declare class SDKConfig {
    private _config;
    constructor(config: SDKConfigData);
}
declare const sdkConfig: SDKConfig;
export { sdkConfig };
export default sdkConfig;
//# sourceMappingURL=config.d.ts.map