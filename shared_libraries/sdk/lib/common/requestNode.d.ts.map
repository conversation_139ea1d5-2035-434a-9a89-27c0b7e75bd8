{"version": 3, "file": "requestNode.d.ts", "sourceRoot": "", "sources": ["../../src/common/requestNode.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,MAAM,EACN,eAAe,EAChB,MAAM,aAAa,CAAC;AAErB,wBAAgB,WAAW,CACzB,IAAI,EAAE,iBAAiB,EACvB,MAAM,CAAC,EAAE,WAAW,EAAE,EACtB,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,EAClB,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,EACpB,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI,GACzB,aAAa,CAmBf;AAED,wBAAgB,eAAe,CAC7B,IAAI,EAAE,iBAAiB,EACvB,IAAI,EAAE,IAAI,EACV,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAEf;AAED,wBAAgB,gBAAgB,CAC9B,IAAI,EAAE,iBAAiB,EACvB,KAAK,EAAE,KAAK,EACZ,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAcf;AAqDD,wBAAgB,gBAAgB,CAC9B,IAAI,EAAE,iBAAiB,EACvB,KAAK,EAAE,KAAK,EACZ,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAEf;AAED,wBAAgB,kBAAkB,CAChC,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,QAAQ,EAClB,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAEf;AAED,wBAAgB,kBAAkB,CAChC,IAAI,EAAE,iBAAiB,EACvB,OAAO,EAAE,YAAY,EACrB,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAMf;AAED,wBAAgB,0BAA0B,CACxC,IAAI,EAAE,iBAAiB,EACvB,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,YAAY,EACrB,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAaf;AAED,wBAAgB,kBAAkB,CAChC,IAAI,EAAE,iBAAiB,EACvB,OAAO,EAAE,OAAO,EAChB,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAUf;AAED,wBAAgB,iBAAiB,CAC/B,IAAI,EAAE,iBAAiB,EACvB,MAAM,EAAE,MAAM,EACd,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAUf;AAED,wBAAgB,0BAA0B,CACxC,IAAI,EAAE,iBAAiB,EACvB,MAAM,EAAE,eAAe,EACvB,MAAM,CAAC,EAAE,WAAW,EAAE,GACrB,aAAa,CAUf"}