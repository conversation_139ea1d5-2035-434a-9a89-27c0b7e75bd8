import { ClientRequestParameter, ClientRequestType, ClientResponseCode, GroupAccessType, UserRole, UserType, } from '@moxo/proto';
import sdkConfig from '../common/config';
import { sendBizServerRequest } from '../common/bizServerRequest';
import { requestNode, userRequestNode } from '../common/requestNode';
import { MxLoginType, MxRegisterUserMethod } from './userDefines';
import { buildServerError } from '../common/bizServerError';
import { MxTokenType, MxVerifyCodeAction } from './userDefines';
import get from 'lodash/get';
export function verifyToken(accessToken = '', rememberMe = true) {
    let params = [];
    if (accessToken && sdkConfig.useCookies) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_READ_SET_COOKIE,
        });
        if (rememberMe) {
            params.push({
                name: ClientRequestParameter.USER_REQUEST_LOGIN_REMEMBER,
            });
        }
    }
    return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_VERIFY_TOKEN, params), {
        accessToken: accessToken,
    });
}
export function getAccessTokenByEmailPassword(opt) {
    let params = [
        {
            name: ClientRequestParameter.USER_REQUEST_GET_ACCESS_TOKEN,
        },
    ];
    if (opt.isSuperAdmin || opt.isPartnerAdmin) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED,
        });
    }
    let user = {
        email: opt.email,
        phone_number: opt.phone_number,
        pass: opt.pass,
        first_name: opt.firstName,
        last_name: opt.lastName,
        title: opt.title,
    };
    if (opt.deviceId) {
        user.user_devices = [{ device_id: opt.deviceId }];
    }
    if (opt.rememberDevice) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_REMEMBER_DEVICE,
        });
    }
    if (opt.verificationCode) {
        params.push({
            name: opt.verificationCodeType === 'email'
                ? ClientRequestParameter.USER_REQUEST_EMAIL_CODE
                : ClientRequestParameter.USER_REQUEST_SMS_CODE,
            string_value: opt.verificationCode,
        });
    }
    if (opt.appleJWT) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
            string_value: opt.appleJWT,
        });
    }
    if (opt.googleJWT) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
            string_value: opt.googleJWT,
        });
    }
    if (opt.salesforceJWT) {
        params.push({
            //is different for some reason
            //name: ClientRequestParameter.USER_REQUEST_SALESFORCE_JWT,
            name: ClientRequestParameter.CLIENT_PARAM_JWT,
            string_value: opt.salesforceJWT,
        });
    }
    if (opt.orgInviteToken) {
        params.push({
            name: ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN,
            string_value: opt.orgInviteToken,
        });
    }
    let reqType = ClientRequestType.USER_REQUEST_LOGIN;
    if (opt.loginType === MxLoginType.VERIFICATION_CODE) {
        reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE;
    }
    else if (opt.loginType === MxLoginType.APPLE_JWT) {
        reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID;
    }
    else if (opt.loginType === MxLoginType.GOOGLE_JWT) {
        reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID;
    }
    else if (opt.loginType === MxLoginType.SALESFORCE_JWT) {
        reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID;
    }
    else if (opt.loginType === MxLoginType.ORG_INVITE_TOKEN) {
        reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN;
    }
    return sendBizServerRequest(userRequestNode(reqType, user, params));
}
export function getAccessToken() {
    return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_ACCESS_TOKEN));
}
export function loginMepUser(opt) {
    return getAccessTokenByEmailPassword(opt).then((response) => {
        // for SA PA, login success will return user object directly
        let token = response.data;
        if (token) {
            sdkConfig.accessToken = token;
        }
        return verifyToken(token, opt.rememberMe);
    });
}
export function logout(logoutAllDevice) {
    let reqType = logoutAllDevice
        ? ClientRequestType.USER_REQUEST_LOGOUT_ALL_DEVICES
        : ClientRequestType.USER_REQUEST_LOGOUT;
    return sendBizServerRequest(requestNode(reqType));
}
export function readPasswordRule() {
    return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_READ_PASSWORD_RULE), {}, (response) => {
        return get(response, 'object.system_config.password_rule');
    });
}
export function readSsoOptions() {
    return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_READ_SSO_OPTIONS));
}
export function decodeViewToken(token, tokenType) {
    let requestType;
    let paramName;
    if (tokenType === MxTokenType.BOARD_VIEW_TOKEN) {
        requestType = ClientRequestType.BOARD_REQUEST_VIEW_INVITATION;
        paramName = ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN;
    }
    else if (tokenType === MxTokenType.GROUP_INVITE_TOKEN) {
        requestType = ClientRequestType.GROUP_REQUEST_VIEW_INVITATION;
        paramName = ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN;
    }
    else if (tokenType === MxTokenType.PARTNER_INVITE_TOKEN) {
        requestType = ClientRequestType.PARTNER_REQUEST_VIEW_INVITATION;
        paramName = ClientRequestParameter.PARTNER_REQUEST_INVITE_TOKEN;
    }
    else if (tokenType === MxTokenType.QR_TOKEN) {
        requestType = ClientRequestType.USER_REQUEST_VIEW_QR_TOKEN;
        paramName = ClientRequestParameter.GROUP_REQUEST_USER_TOKEN;
    }
    else if (tokenType === MxTokenType.EMAIL_VERIFICATION_TOKEN) {
        requestType = ClientRequestType.USER_REQUEST_PREVIEW_EMAIL_TOKEN;
        return sendBizServerRequest(userRequestNode(requestType, { email_verification_token: token }));
    }
    else {
        return Promise.reject(buildServerError('invalid params', ClientResponseCode.RESPONSE_ERROR_INVALID_REQUEST));
    }
    let params = [
        {
            name: paramName,
            string_value: token,
        },
    ];
    return sendBizServerRequest(requestNode(requestType, params));
}
export function verifyEmailToken(token) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_TOKEN, {
        email_verification_token: token,
    }));
}
export function verifyEmailCode(email, code, userId) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE, {
        email: email,
        email_verification_code: code,
        id: userId,
    }));
}
export function verifyGlobalEmailCode(email, code) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_CODE, {
        email: email,
        email_verification_code: code,
    }));
}
export function verifyGlobalSmsCode(phoneNum, code) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_CODE, {
        phone_number: phoneNum,
        email_verification_code: code,
    }));
}
export function verifySmsCode(phoneNum, code, userId) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_SMS_CODE, {
        phone_number: phoneNum,
        email_verification_code: code,
        id: userId,
    }));
}
export function verifyAppleJWT(jwt) {
    let params = [
        {
            name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
            string_value: jwt,
        },
    ];
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_APPLE_JWT, {}, params));
}
export function verifyGoogleJWT(jwt) {
    let params = [
        {
            name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
            string_value: jwt,
        },
    ];
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT, {}, params));
}
export function resendVerifyCodeEmail(email, qrToken, action, userId) {
    let params = [];
    if (qrToken) {
        params.push({
            name: ClientRequestParameter.GROUP_REQUEST_USER_TOKEN,
            string_value: qrToken,
        });
    }
    if (action === MxVerifyCodeAction.REGISTER) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_CODE_TO_REGISTER,
        });
    }
    else if (action === MxVerifyCodeAction.RESET_PASSWORD) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_CODE_TO_RESET_PASSWORD,
        });
    }
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL, { email: email, id: userId }, params));
}
export function resendGlobalVerifyCodeEmail(email) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL, {
        email: email,
    }));
}
export function resendGlobalVerifyCodeSms(phoneNum) {
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL, {
        phone_number: phoneNum,
    }));
}
export function resendVerifyCodeSms(phoneNum, qrToken, action, userId) {
    let params = [];
    if (qrToken) {
        params.push({
            name: ClientRequestParameter.GROUP_REQUEST_USER_TOKEN,
            string_value: qrToken,
        });
    }
    if (action === MxVerifyCodeAction.REGISTER) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_CODE_TO_REGISTER,
        });
    }
    else if (action === MxVerifyCodeAction.RESET_PASSWORD) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_CODE_TO_RESET_PASSWORD,
        });
    }
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS, { phone_number: phoneNum, id: userId }, params));
}
export function changePassword(old_pass, pass) {
    let params = [];
    let user = {
        pass: pass,
        old_pass: old_pass,
    };
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_CHANGE_PASSWORD, user, params));
}
export function resetPassword(email, isPartnerAdminExpected) {
    let user = {
        email: email,
    };
    let params = [];
    if (isPartnerAdminExpected) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED,
        });
    }
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESET_PASSWORD, user, params), {}, () => {
        return void 0;
    });
}
export function resetPasswordWithPhoneNum(phoneNum, verifyCode, password) {
    let params = [];
    let user = {
        phone_number: phoneNum,
        email_verification_code: verifyCode,
        pass: password,
    };
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESET_PASSWORD, user, params));
}
export function resetPasswordWithEmail(email, verifyCode, password) {
    let params = [];
    let user = {
        email: email,
        email_verification_code: verifyCode,
        pass: password,
    };
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESET_PASSWORD, user, params));
}
export function resetPasswordWithToken(pass, token, isPartnerAdminExpected) {
    let params = [];
    let user = {
        pass: pass,
        email_verification_token: token,
    };
    if (isPartnerAdminExpected) {
        params.push({
            name: ClientRequestParameter.USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED,
        });
    }
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESET_PASSWORD, user, params));
}
export function resendVerificationEmail(clientRelationSeq) {
    let user = {};
    if (clientRelationSeq) {
        user.relations = [{ sequence: clientRelationSeq }];
    }
    return sendBizServerRequest(userRequestNode(ClientRequestType.USER_REQUEST_RESEND_VERIFICATION_EMAIL, user));
}
export function resendViewToken(oldViewToken) {
    let params = [
        {
            name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
            string_value: oldViewToken,
        },
    ];
    return sendBizServerRequest(requestNode(ClientRequestType.BOARD_REQUEST_RESEND_VIEW_TOKEN, params));
}
export function registerUser(user, token, isPartnerAdmin) {
    let params = [];
    let type = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER;
    let tokenName = ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN;
    if (isPartnerAdmin) {
        type = ClientRequestType.USER_REQUEST_REGISTER;
        tokenName = ClientRequestParameter.PARTNER_REQUEST_INVITE_TOKEN;
        params.push({
            name: ClientRequestParameter.USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED,
        });
    }
    if (token) {
        params.push({
            name: tokenName,
            string_value: token,
        });
    }
    return sendBizServerRequest(userRequestNode(type, user, params));
}
export function registerFreemiumUser(user, option) {
    let params = [];
    const { method, authorization, qrToken, inviteToken, isInternalUser, noRelationBoard } = option;
    if (noRelationBoard) {
        params.push({
            name: ClientRequestParameter.GROUP_REQUEST_NO_RELATION_BOARD,
        });
    }
    if (!isInternalUser)
        user.type = UserType.USER_TYPE_LOCAL;
    if (inviteToken) {
        params.push({
            name: ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN,
            string_value: inviteToken,
        });
    }
    if (qrToken) {
        user.relations = [
            {
                user: {
                    qr_tokens: [
                        {
                            token: qrToken,
                        },
                    ],
                },
            },
        ];
    }
    let reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN;
    if (method === MxRegisterUserMethod.INVITE_TOKEN || method === MxRegisterUserMethod.QR_TOKEN) {
        reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN;
    }
    else if (method === MxRegisterUserMethod.APPLE_JWT) {
        reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID;
        if (inviteToken) {
            reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID;
        }
        params.push({
            name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
            string_value: authorization,
        });
    }
    else if (method === MxRegisterUserMethod.GOOGLE_JWT) {
        reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID;
        if (inviteToken) {
            reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID;
        }
        params.push({
            name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
            string_value: authorization,
        });
    }
    else if (method === MxRegisterUserMethod.EMAIL_CODE) {
        reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE;
        params.push({
            name: ClientRequestParameter.USER_REQUEST_EMAIL_CODE,
            string_value: authorization,
        });
    }
    else if (method === MxRegisterUserMethod.SMS_CODE) {
        reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE;
        params.push({
            name: ClientRequestParameter.USER_REQUEST_SMS_CODE,
            string_value: authorization,
        });
    }
    return sendBizServerRequest(userRequestNode(reqType, user, params));
}
export function checkUserPermission(user, loginOpts) {
    if (!user) {
        throw buildServerError('user expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
    }
    let userGroups = (user.groups || []).filter((group) => !group.is_deleted);
    let userPartners = (user.partners || []).filter((partner) => !partner.is_deleted);
    let userRole = user.role || UserRole.USER_ROLE_NORMAL;
    let userGroupRole = GroupAccessType.GROUP_NO_ACCESS;
    if (userGroups.length > 0 && userGroups[0].type) {
        userGroupRole = userGroups[0].type;
    }
    const ORG_ADMIN_ROLES = [
        GroupAccessType.GROUP_OWNER_ACCESS,
        GroupAccessType.GROUP_ADMIN_ACCESS,
    ];
    const SUPER_ADMIN_ROLES = [
        UserRole.USER_ROLE_SUPERADMIN,
        UserRole.USER_ROLE_OBJECT_READ,
        UserRole.USER_ROLE_OBJECT_WRITE,
        UserRole.USER_ROLE_SUPERADMIN_READONLY,
    ];
    let isOrgAdmin = ORG_ADMIN_ROLES.indexOf(userGroupRole) > -1;
    let isSuperAdmin = SUPER_ADMIN_ROLES.indexOf(userRole) > -1;
    let isPartnerAdmin = userPartners.length > 0;
    let userOrgId = userGroups.length > 0 ? userGroups[0].group.id : '';
    let expectedRoles = loginOpts || {
        isOrgAdmin: false,
        isSuperAdmin: false,
        isPartnerAdmin: false,
    };
    let expectIsOrgAdmin = expectedRoles.isOrgAdmin || false;
    let expectIsSuperAdmin = expectedRoles.isSuperAdmin || false;
    let expectIsPartnerAdmin = expectedRoles.isPartnerAdmin || false;
    let expectNormalUser = !(expectIsOrgAdmin || expectIsSuperAdmin || expectIsPartnerAdmin);
    let expectUserOrgId = ''; //cacheMgr.anonymousOrg ? cacheMgr.anonymousOrg.id : '';
    if (expectNormalUser) {
        // do not allow SA PA to access web portal
        if (userOrgId && expectUserOrgId === userOrgId) {
            // for dirty data: some super admin account has user group, allow it to access web portal
        }
        else if (isSuperAdmin || isPartnerAdmin) {
            throw buildServerError('normal user expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
        }
    }
    if (expectIsOrgAdmin) {
        // allow OA SA PA to access admin portal
        if (!isOrgAdmin && !isSuperAdmin && !isPartnerAdmin) {
            throw buildServerError('org admin expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
        }
    }
    if (expectIsPartnerAdmin) {
        // allow SA PA to access partner admin portal
        if (!isSuperAdmin && !isPartnerAdmin) {
            throw buildServerError('partner admin expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
        }
    }
    if (expectIsSuperAdmin) {
        // allow SA to access super admin portal
        if (!isSuperAdmin) {
            throw buildServerError('super admin expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
        }
    }
    return {
        isOrgAdmin: expectIsOrgAdmin,
        isSuperAdmin: expectIsSuperAdmin,
        isPartnerAdmin: expectIsPartnerAdmin,
    };
}
export function sendUserKeepAliveRequest() {
    return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_KEEP_ALIVE));
}
