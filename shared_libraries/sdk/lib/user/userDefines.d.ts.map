{"version": 3, "file": "userDefines.d.ts", "sourceRoot": "", "sources": ["../../src/user/userDefines.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAClD,oBAAY,WAAW;IACrB,SAAS,cAAc;IACvB,UAAU,eAAe;IACzB,cAAc,mBAAmB;IACjC,iBAAiB,sBAAsB;IACvC,gBAAgB,qBAAqB;CACtC;AACD,oBAAY,oBAAoB;IAC9B,YAAY,iBAAiB;IAC7B,QAAQ,aAAa;IACrB,SAAS,cAAc;IACvB,UAAU,eAAe;IACzB,UAAU,eAAe;IACzB,QAAQ,aAAa;CACtB;AACD,MAAM,WAAW,QAAQ;IACvB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,oBAAoB;IACnC,MAAM,CAAC,EAAE,oBAAoB,CAAC;IAC9B,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AACD,MAAM,WAAW,WAAW;IAC1B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,cAAc,CAAC,EAAE,OAAO,CAAC;IAGzB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,cAAc,CAAC,EAAE,OAAO,CAAC;IAGzB,SAAS,CAAC,EAAE,WAAW,CAAC;IACxB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,cAAc,CAAC,EAAE,MAAM,CAAC;IAGxB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AACD,oBAAY,WAAW;IACrB,kBAAkB,IAAA;IAClB,oBAAoB,IAAA;IACpB,gBAAgB,IAAA;IAChB,QAAQ,IAAA;IACR,wBAAwB,IAAA;CACzB;AAED,oBAAY,kBAAkB;IAC5B,QAAQ,aAAa;IACrB,cAAc,mBAAmB;CAClC;AAED,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAE1D,KAAK,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAE5C,oBAAoB,CAAC,KAAK,EAAE,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAE/D,MAAM,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IAEzD,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAE1F,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,sBAAsB,CAAC,EAAE,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IAEtF,yBAAyB,CACvB,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,GACf,eAAe,CAAC,IAAI,CAAC,CAAC;IAEzB,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,GACf,eAAe,CAAC,IAAI,CAAC,CAAC;IAEzB,sBAAsB,CACpB,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,sBAAsB,CAAC,EAAE,OAAO,GAC/B,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,gBAAgB,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;CACzD"}