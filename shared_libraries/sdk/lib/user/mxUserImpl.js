var _a;
import { MxLoginType } from './userDefines';
import { RequestPromise } from '../network/ajax';
import { checkUserPermission, loginMepUser, verifyToken, logout, resetPassword, resetPasswordWithEmail, resetPasswordWithPhoneNum, readPasswordRule, registerUser, } from './auth';
import { getUserAvatar, getUserInitial, getUserName } from './transformUser';
const UserSymbol = Symbol('user');
export class MxUserImpl {
    get isLogin() {
        var _b;
        return !!((_b = this[UserSymbol]) === null || _b === void 0 ? void 0 : _b.id);
    }
    get id() {
        return this[UserSymbol].id || '';
    }
    get userInfo() {
        let user = this[UserSymbol];
        return {
            id: user.id,
            name: getUserName(user),
            email: user.email,
            phoneNumber: user.phone_number,
            avatar: getUserAvatar(user),
            initials: getU<PERSON><PERSON><PERSON><PERSON>(user),
        };
    }
    constructor(user) {
        Object.defineProperty(this, _a, {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this[UserSymbol] = user || { id: '' };
    }
    verifyToken(opt) {
        if (opt && opt.notLoadFullUser) {
            return verifyToken().then((response) => {
                var _b;
                const user = ((_b = response.object) === null || _b === void 0 ? void 0 : _b.user) || { id: '' };
                this[UserSymbol] = user;
                return this.userInfo;
            });
        }
        else {
            return this.login(opt);
        }
    }
    loginWithAccessToken(token) {
        return verifyToken(token, true).then((response) => {
            var _b;
            const user = ((_b = response.object) === null || _b === void 0 ? void 0 : _b.user) || { id: '' };
            this[UserSymbol] = user;
            return this.userInfo;
        });
    }
    logout(logoutAllDevice) {
        return logout(logoutAllDevice);
    }
    register(user, token) {
        return registerUser(user, token).then((response) => {
            return this.loginWithAccessToken(response.data || '');
        });
    }
    resetPassword(email, isPartnerAdminExpected) {
        return resetPassword(email, isPartnerAdminExpected);
    }
    resetPasswordWithPhoneNum(phoneNum, verifyCode, password) {
        return resetPasswordWithPhoneNum(phoneNum, verifyCode, password).then(() => {
            return void 0;
        });
    }
    resetPasswordWithEmail(email, verifyCode, password) {
        return resetPasswordWithEmail(email, verifyCode, password).then(() => {
            return void 0;
        });
    }
    resetPasswordWithToken(pass, token, isPartnerAdminExpected) {
        return this.resetPasswordWithToken(pass, token, isPartnerAdminExpected);
    }
    readPasswordRule() {
        return readPasswordRule();
    }
    login(opt) {
        let isAutoLogin = !opt || !opt.pass;
        if (opt) {
            if (opt.loginType === MxLoginType.VERIFICATION_CODE ||
                opt.loginType === MxLoginType.ORG_INVITE_TOKEN ||
                opt.loginType === MxLoginType.APPLE_JWT ||
                opt.loginType === MxLoginType.GOOGLE_JWT ||
                opt.loginType === MxLoginType.SALESFORCE_JWT) {
                isAutoLogin = false;
            }
        }
        else {
            opt = {};
        }
        if (isAutoLogin && this.isLogin) {
            // already login?
            return RequestPromise.createResolve(this.userInfo);
        }
        let req;
        if (isAutoLogin) {
            req = verifyToken();
        }
        else {
            req = loginMepUser(opt);
        }
        return req.then((response) => {
            var _b;
            const user = ((_b = response.object) === null || _b === void 0 ? void 0 : _b.user) || { id: '' };
            checkUserPermission(user, opt);
            this[UserSymbol] = user;
            return this.userInfo;
        });
    }
}
_a = UserSymbol;
const MxUser = new MxUserImpl();
export { MxUser };
