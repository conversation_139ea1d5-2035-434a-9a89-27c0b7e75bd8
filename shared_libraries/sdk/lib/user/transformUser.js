import sdkConfig from '../common/config';
export function getUserName(user) {
    user = user || {};
    const nameList = [];
    if (user.first_name) {
        nameList.push(user.first_name.trim());
    }
    if (user.last_name) {
        nameList.push(user.last_name.trim());
    }
    let name = '';
    if (nameList.length) {
        name = nameList.join(' ').trim();
    }
    else {
        name = user.name || user.email || user.unique_id || user.phone_number || '';
    }
    return name;
}
export function getUserAvatar(user) {
    if (user.picture) {
        return `${sdkConfig.contextPath}/user/${user.picture}`;
    }
    else {
        return '';
    }
}
export function getUserInitial({ first_name = '', last_name = '' }) {
    var _a, _b;
    const firstInitial = (_a = first_name.at(0)) !== null && _a !== void 0 ? _a : '';
    const lastInitial = (_b = last_name.at(0)) !== null && _b !== void 0 ? _b : '';
    return `${firstInitial}${lastInitial}`.toUpperCase();
}
