export var MxLoginType;
(function (MxLoginType) {
    MxLoginType["APPLE_JWT"] = "APPLE_JWT";
    MxLoginType["GOOGLE_JWT"] = "GOOGLE_JWT";
    MxLoginType["SALESFORCE_JWT"] = "SALESFORCE_JWT";
    MxLoginType["VERIFICATION_CODE"] = "VERIFICATION_CODE";
    MxLoginType["ORG_INVITE_TOKEN"] = "ORG_INVITE_TOKEN";
})(MxLoginType || (MxLoginType = {}));
export var MxRegisterUserMethod;
(function (MxRegisterUserMethod) {
    MxRegisterUserMethod["INVITE_TOKEN"] = "INVITE_TOKEN";
    MxRegisterUserMethod["QR_TOKEN"] = "QR_TOKEN";
    MxRegisterUserMethod["APPLE_JWT"] = "APPLE_JWT";
    MxRegisterUserMethod["GOOGLE_JWT"] = "GOOGLE_JWT";
    MxRegisterUserMethod["EMAIL_CODE"] = "EMAIL_CODE";
    MxRegisterUserMethod["SMS_CODE"] = "SMS_CODE";
})(MxRegisterUserMethod || (MxRegisterUserMethod = {}));
export var MxTokenType;
(function (MxTokenType) {
    MxTokenType[MxTokenType["GROUP_INVITE_TOKEN"] = 0] = "GROUP_INVITE_TOKEN";
    MxTokenType[MxTokenType["PARTNER_INVITE_TOKEN"] = 1] = "PARTNER_INVITE_TOKEN";
    MxTokenType[MxTokenType["BOARD_VIEW_TOKEN"] = 2] = "BOARD_VIEW_TOKEN";
    MxTokenType[MxTokenType["QR_TOKEN"] = 3] = "QR_TOKEN";
    MxTokenType[MxTokenType["EMAIL_VERIFICATION_TOKEN"] = 4] = "EMAIL_VERIFICATION_TOKEN";
})(MxTokenType || (MxTokenType = {}));
export var MxVerifyCodeAction;
(function (MxVerifyCodeAction) {
    MxVerifyCodeAction["REGISTER"] = "REGISTER";
    MxVerifyCodeAction["RESET_PASSWORD"] = "RESET_PASSWORD";
})(MxVerifyCodeAction || (MxVerifyCodeAction = {}));
