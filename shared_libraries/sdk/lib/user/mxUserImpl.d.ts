import { IMxUser, LoginOption, UserInfo } from './userDefines';
import { SystemPasswordRule, User } from '@moxo/proto';
import { IRequestPromise, RequestPromise } from '../network/ajax';
declare const UserSymbol: unique symbol;
export declare class MxUserImpl implements IMxUser {
    [UserSymbol]: User;
    get isLogin(): boolean;
    get id(): string;
    get userInfo(): UserInfo;
    constructor(user?: User);
    verifyToken(opt?: LoginOption): IRequestPromise<UserInfo>;
    loginWithAccessToken(token: string): IRequestPromise<UserInfo>;
    logout(logoutAllDevice?: boolean): IRequestPromise<void>;
    register(user: User, token: string): IRequestPromise<UserInfo>;
    resetPassword(email: string, isPartnerAdminExpected?: boolean): IRequestPromise<void>;
    resetPasswordWithPhoneNum(phoneNum: string, verifyCode: string, password: string): IRequestPromise<void>;
    resetPasswordWithEmail(email: string, verifyCode: string, password: string): IRequestPromise<void>;
    resetPasswordWithToken(pass: string, token: string, isPartnerAdminExpected?: boolean): IRequestPromise<void>;
    readPasswordRule(): IRequestPromise<SystemPasswordRule>;
    login(opt?: LoginOption): RequestPromise<UserInfo>;
}
declare const MxUser: MxUserImpl;
export { MxUser };
//# sourceMappingURL=mxUserImpl.d.ts.map