{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/user/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAIL,cAAc,EAGd,kBAAkB,EAClB,IAAI,EAGL,MAAM,aAAa,CAAC;AAGrB,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAElE,OAAO,EAAE,WAAW,EAAqC,MAAM,eAAe,CAAC;AAE/E,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AAGtF,wBAAgB,WAAW,CACzB,WAAW,GAAE,MAAW,EACxB,UAAU,GAAE,OAAc,GACzB,eAAe,CAAC,cAAc,CAAC,CAkBjC;AACD,wBAAgB,6BAA6B,CAAC,GAAG,EAAE,WAAW,GAAG,eAAe,CAAC,cAAc,CAAC,CAsF/F;AACD,wBAAgB,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC,CAE/D;AACD,wBAAgB,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG,eAAe,CAAC,cAAc,CAAC,CAU9E;AAED,wBAAgB,MAAM,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,CAKtE;AAED,wBAAgB,gBAAgB,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAQtE;AAED,wBAAgB,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,CAExD;AAED,wBAAgB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAiC/F;AAED,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAMvE;AAED,wBAAgB,eAAe,CAC7B,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC,cAAc,CAAC,CAQzB;AAED,wBAAgB,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAO1F;AAED,wBAAgB,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAO3F;AAED,wBAAgB,aAAa,CAC3B,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC,cAAc,CAAC,CAQzB;AAED,wBAAgB,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAWnE;AAED,wBAAgB,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAWpE;AAED,wBAAgB,qBAAqB,CACnC,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,MAAM,EAChB,MAAM,CAAC,EAAE,kBAAkB,EAC3B,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC,cAAc,CAAC,CA0BzB;AAED,wBAAgB,2BAA2B,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAMlF;AAED,wBAAgB,yBAAyB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAMnF;AAED,wBAAgB,mBAAmB,CACjC,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,MAAM,EAChB,MAAM,CAAC,EAAE,kBAAkB,EAC3B,MAAM,CAAC,EAAE,MAAM,GACd,OAAO,CAAC,cAAc,CAAC,CA0BzB;AAED,wBAAgB,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAUtF;AAED,wBAAgB,aAAa,CAC3B,KAAK,EAAE,MAAM,EACb,sBAAsB,CAAC,EAAE,OAAO,GAC/B,eAAe,CAAC,IAAI,CAAC,CAmBvB;AAED,wBAAgB,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,4BAW/F;AAED,wBAAgB,sBAAsB,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,4BAWzF;AAED,wBAAgB,sBAAsB,CACpC,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,sBAAsB,CAAC,EAAE,OAAO,4BAkBjC;AAED,wBAAgB,uBAAuB,CAAC,iBAAiB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAQ3F;AACD,wBAAgB,eAAe,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAW7E;AACD,wBAAgB,YAAY,CAC1B,IAAI,EAAE,IAAI,EACV,KAAK,CAAC,EAAE,MAAM,EACd,cAAc,CAAC,EAAE,OAAO,GACvB,eAAe,CAAC,cAAc,CAAC,CAoBjC;AACD,wBAAgB,oBAAoB,CAClC,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,oBAAoB,GAC3B,cAAc,CAAC,IAAI,CAAC,CAsEtB;AAED,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW;;;;EA8ErE;AAED,wBAAgB,wBAAwB,6BAEvC"}