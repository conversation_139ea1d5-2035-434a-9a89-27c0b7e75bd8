import { ClientResponse, SystemPasswordRule, User } from '@moxo/proto';
import { IRequestPromise, RequestPromise } from '../network/ajax';
import { LoginOption } from './userDefines';
import { MxRegisterUserOption, MxTokenType, MxVerifyCodeAction } from './userDefines';
export declare function verifyToken(accessToken?: string, rememberMe?: boolean): IRequestPromise<ClientResponse>;
export declare function getAccessTokenByEmailPassword(opt: LoginOption): IRequestPromise<ClientResponse>;
export declare function getAccessToken(): RequestPromise<ClientResponse>;
export declare function loginMepUser(opt: LoginOption): IRequestPromise<ClientResponse>;
export declare function logout(logoutAllDevice?: boolean): RequestPromise<void>;
export declare function readPasswordRule(): IRequestPromise<SystemPasswordRule>;
export declare function readSsoOptions(): Promise<ClientResponse>;
export declare function decodeViewToken(token: string, tokenType?: MxTokenType): Promise<ClientResponse>;
export declare function verifyEmailToken(token: string): Promise<ClientResponse>;
export declare function verifyEmailCode(email: string, code: string, userId?: string): Promise<ClientResponse>;
export declare function verifyGlobalEmailCode(email: string, code: string): Promise<ClientResponse>;
export declare function verifyGlobalSmsCode(phoneNum: string, code: string): Promise<ClientResponse>;
export declare function verifySmsCode(phoneNum: string, code: string, userId?: string): Promise<ClientResponse>;
export declare function verifyAppleJWT(jwt: string): Promise<ClientResponse>;
export declare function verifyGoogleJWT(jwt: string): Promise<ClientResponse>;
export declare function resendVerifyCodeEmail(email: string, qrToken?: string, action?: MxVerifyCodeAction, userId?: string): Promise<ClientResponse>;
export declare function resendGlobalVerifyCodeEmail(email: string): Promise<ClientResponse>;
export declare function resendGlobalVerifyCodeSms(phoneNum: string): Promise<ClientResponse>;
export declare function resendVerifyCodeSms(phoneNum: string, qrToken?: string, action?: MxVerifyCodeAction, userId?: string): Promise<ClientResponse>;
export declare function changePassword(old_pass: string, pass: string): Promise<ClientResponse>;
export declare function resetPassword(email: string, isPartnerAdminExpected?: boolean): IRequestPromise<void>;
export declare function resetPasswordWithPhoneNum(phoneNum: string, verifyCode: string, password: string): IRequestPromise<unknown>;
export declare function resetPasswordWithEmail(email: string, verifyCode: string, password: string): IRequestPromise<unknown>;
export declare function resetPasswordWithToken(pass: string, token: string, isPartnerAdminExpected?: boolean): IRequestPromise<unknown>;
export declare function resendVerificationEmail(clientRelationSeq?: number): Promise<ClientResponse>;
export declare function resendViewToken(oldViewToken: string): Promise<ClientResponse>;
export declare function registerUser(user: User, token?: string, isPartnerAdmin?: boolean): IRequestPromise<ClientResponse>;
export declare function registerFreemiumUser(user: User, option: MxRegisterUserOption): RequestPromise<User>;
export declare function checkUserPermission(user: User, loginOpts: LoginOption): {
    isOrgAdmin: boolean;
    isSuperAdmin: boolean;
    isPartnerAdmin: boolean;
};
export declare function sendUserKeepAliveRequest(): IRequestPromise<unknown>;
//# sourceMappingURL=auth.d.ts.map