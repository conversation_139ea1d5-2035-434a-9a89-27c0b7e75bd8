import { ClientRequest, ClientResponse } from '@moxo/proto';
export declare enum AjaxMethod {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE"
}
export declare enum AjaxContentType {
    PROTOBUF = "application/x-protobuf",
    JSON = "application/json",
    TEXT = "text/plain",
    MULTIPART = "multipart/form-data",
    HTML = "text/html",
    FORM = "application/x-www-form-urlencoded"
}
export interface RequestParser {
    encode: (json: ClientRequest) => ArrayBuffer;
    decode: (buffer: ArrayBuffer) => ClientResponse;
}
export interface IRequestParam {
    method: AjaxMethod;
    contentType?: AjaxContentType;
    nocache?: boolean;
    queryParams?: Record<string, string | number>;
    accessToken?: string;
    timeout?: number;
    authorizationHeader?: string;
    responseType?: string;
    block?: boolean;
    parser?: RequestParser;
}
export interface IRequestPromise<T> extends Promise<T> {
    readonly requestId: string;
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): IRequestPromise<TResult1 | TResult2>;
}
export declare class RequestPromise<T> extends Promise<T> implements IRequestPromise<T> {
    readonly requestId: string;
    constructor(executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: unknown) => void) => void, requestId: string);
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): IRequestPromise<TResult1 | TResult2>;
    static fromPromise<T>(promise: Promise<T>, requestId: string): RequestPromise<T>;
    static createResolve<T>(response: T): RequestPromise<T>;
    static createReject<T>(reason?: T): RequestPromise<T>;
}
/**
 * 发送网络请求（支持阻塞/非阻塞模式，可取消）
 * @param url 请求URL
 * @param opts 请求配置参数
 * @param body 请求体数据
 * @returns 带有requestId的Promise对象，可通过abortRequest取消
 *
 * @example
 * // 基本用法
 * const req = sendRequest('/api/data', { method: 'GET' });
 * req.then(data => console.log(data));
 *
 * // 阻塞请求（会等待前一个阻塞请求完成）
 * const req = sendRequest('/api/data', { method: 'GET', block: true });
 *
 * // 多内容上传
 * const body = new Map()
 * body.set('file1', blob)
 * body.set('file2', blob)
 * sendRequest('/api/data', { method: 'POST' },body)
 *
 * // 取消请求
 * abortRequest(req);
 */
export declare function sendRequest(url: string, opts: IRequestParam, body?: unknown | Map<string, Blob>): RequestPromise<unknown>;
/**
 * 取消指定请求
 * @param request 由sendRequest返回的请求对象或请求ID
 */
export declare function abortRequest(request: string | IRequestPromise<unknown>): void;
//# sourceMappingURL=ajax.d.ts.map