import { User } from '@moxo/proto';
import sdkConfig from '../common/config';

export function getUserName(user: User): string {
  user = user || {};
  const nameList = [];
  if (user.first_name) {
    nameList.push(user.first_name.trim());
  }
  if (user.last_name) {
    nameList.push(user.last_name.trim());
  }
  let name: string = '';
  if (nameList.length) {
    name = nameList.join(' ').trim();
  } else {
    name = user.name || user.email || user.unique_id || user.phone_number || '';
  }
  return name;
}
export function getUserAvatar(user: User) {
  if (user.picture) {
    return `${sdkConfig.contextPath}/user/${user.picture}`;
  } else {
    return '';
  }
}
export function getUserInitial({ first_name = '', last_name = '' }: User): string {
  const firstInitial = first_name.at(0) ?? '';
  const lastInitial = last_name.at(0) ?? '';
  return `${firstInitial}${lastInitial}`.toUpperCase();
}
