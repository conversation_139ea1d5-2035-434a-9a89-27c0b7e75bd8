import { IMxUser, LoginOption, MxLoginType, UserInfo } from './userDefines';
import { ClientResponse, SystemPasswordRule, User } from '@moxo/proto';
import { IRequestPromise, RequestPromise } from '../network/ajax';
import {
  checkUserPermission,
  loginMepUser,
  verifyToken,
  logout,
  resetPassword,
  resetPasswordWithEmail,
  resetPasswordWithPhoneNum,
  readPasswordRule,
  registerUser,
} from './auth';
import { getUserAvatar, getUserInitial, getUserName } from './transformUser';

const UserSymbol = Symbol('user');

export class MxUserImpl implements IMxUser {
  [UserSymbol]: User;

  get isLogin() {
    return !!this[UserSymbol]?.id;
  }

  get id(): string {
    return this[UserSymbol].id || '';
  }

  get userInfo(): UserInfo {
    let user = this[UserSymbol];
    return {
      id: user.id,
      name: getU<PERSON><PERSON><PERSON>(user),
      email: user.email,
      phoneNumber: user.phone_number,
      avatar: getUser<PERSON>vatar(user),
      initials: getU<PERSON><PERSON><PERSON><PERSON>(user),
    } as UserInfo;
  }

  constructor(user?: User) {
    this[UserSymbol] = user || { id: '' };
  }

  verifyToken(opt?: LoginOption): IRequestPromise<UserInfo> {
    if (opt && opt.notLoadFullUser) {
      return verifyToken().then((response: ClientResponse) => {
        const user = response.object?.user || { id: '' };
        this[UserSymbol] = user;
        return this.userInfo;
      });
    } else {
      return this.login(opt);
    }
  }
  loginWithAccessToken(token: string): IRequestPromise<UserInfo> {
    return verifyToken(token, true).then((response) => {
      const user = response.object?.user || { id: '' };
      this[UserSymbol] = user;
      return this.userInfo;
    });
  }
  logout(logoutAllDevice?: boolean): IRequestPromise<void> {
    return logout(logoutAllDevice);
  }
  register(user: User, token: string): IRequestPromise<UserInfo> {
    return registerUser(user, token).then((response) => {
      return this.loginWithAccessToken(response.data || '');
    });
  }
  resetPassword(email: string, isPartnerAdminExpected?: boolean): IRequestPromise<void> {
    return resetPassword(email, isPartnerAdminExpected);
  }
  resetPasswordWithPhoneNum(
    phoneNum: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void> {
    return resetPasswordWithPhoneNum(phoneNum, verifyCode, password).then(() => {
      return void 0;
    });
  }
  resetPasswordWithEmail(
    email: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void> {
    return resetPasswordWithEmail(email, verifyCode, password).then(() => {
      return void 0;
    });
  }
  resetPasswordWithToken(
    pass: string,
    token: string,
    isPartnerAdminExpected?: boolean,
  ): IRequestPromise<void> {
    return this.resetPasswordWithToken(pass, token, isPartnerAdminExpected);
  }
  readPasswordRule(): IRequestPromise<SystemPasswordRule> {
    return readPasswordRule();
  }

  login(opt?: LoginOption): RequestPromise<UserInfo> {
    let isAutoLogin: boolean = !opt || !opt.pass;
    if (opt) {
      if (
        opt.loginType === MxLoginType.VERIFICATION_CODE ||
        opt.loginType === MxLoginType.ORG_INVITE_TOKEN ||
        opt.loginType === MxLoginType.APPLE_JWT ||
        opt.loginType === MxLoginType.GOOGLE_JWT ||
        opt.loginType === MxLoginType.SALESFORCE_JWT
      ) {
        isAutoLogin = false;
      }
    } else {
      opt = {};
    }

    if (isAutoLogin && this.isLogin) {
      // already login?
      return RequestPromise.createResolve(this.userInfo);
    }
    let req: RequestPromise<ClientResponse>;
    if (isAutoLogin) {
      req = verifyToken();
    } else {
      req = loginMepUser(opt);
    }
    return req.then((response) => {
      const user = response.object?.user || { id: '' };
      checkUserPermission(user, opt);
      this[UserSymbol] = user;

      return this.userInfo;
    });
  }
}
const MxUser = new MxUserImpl();
export { MxUser };
