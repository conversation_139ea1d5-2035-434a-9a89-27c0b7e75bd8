import { User, SystemPasswordRule } from '@moxo/proto';
import { IRequestPromise } from '../network/ajax';
export enum MxLoginType {
  APPLE_JWT = 'APPLE_JWT',
  GOOGLE_JWT = 'GOOGLE_JWT',
  SALESFORCE_JWT = 'SALESFORCE_JWT',
  VERIFICATION_CODE = 'VERIFICATION_CODE',
  ORG_INVITE_TOKEN = 'ORG_INVITE_TOKEN',
}
export enum MxRegisterUserMethod {
  INVITE_TOKEN = 'INVITE_TOKEN',
  QR_TOKEN = 'QR_TOKEN',
  APPLE_JWT = 'APPLE_JWT',
  GOOGLE_JWT = 'GOOGLE_JWT',
  EMAIL_CODE = 'EMAIL_CODE',
  SMS_CODE = 'SMS_CODE',
}
export interface UserInfo {
  id: string;
  name: string;
  avatar: string;
  initials: string;
  email: string;
  phoneNumber: string;
}

export interface MxRegisterUserOption {
  method?: MxRegisterUserMethod;
  authorization?: string;

  qrToken?: string;
  inviteToken?: string;
  isInternalUser?: boolean;
  noRelationBoard?: boolean;
}
export interface LoginOption {
  email?: string;
  phone_number?: string;
  pass?: string;
  rememberMe?: boolean;
  notLoadFullUser?: boolean;
  isMep?: boolean;
  isOrgAdmin?: boolean;
  isSuperAdmin?: boolean;
  isPartnerAdmin?: boolean;

  // 2fa support
  deviceId?: string;
  verificationCode?: string;
  verificationCodeType?: string; // email / sms
  rememberDevice?: boolean;

  // password free
  loginType?: MxLoginType;
  appleJWT?: string;
  googleJWT?: string;
  salesforceJWT?: string;
  orgInviteToken?: string;

  // for pending user, allow to update name, title at login phase
  firstName?: string;
  lastName?: string;
  title?: string;
}
export enum MxTokenType {
  GROUP_INVITE_TOKEN,
  PARTNER_INVITE_TOKEN,
  BOARD_VIEW_TOKEN,
  QR_TOKEN,
  EMAIL_VERIFICATION_TOKEN,
}

export enum MxVerifyCodeAction {
  REGISTER = 'REGISTER',
  RESET_PASSWORD = 'RESET_PASSWORD',
}

export interface IMxUser {
  readonly id: string;
  verifyToken(opt?: LoginOption): IRequestPromise<UserInfo>;

  login(opt?: LoginOption): Promise<UserInfo>;

  loginWithAccessToken(token: string): IRequestPromise<UserInfo>;

  logout(logoutAllDevice?: boolean): IRequestPromise<void>;

  register(user: User, token?: string, isPartnerAdmin?: boolean): IRequestPromise<UserInfo>;

  resetPassword(email: string, isPartnerAdminExpected?: boolean): IRequestPromise<void>;

  resetPasswordWithPhoneNum(
    phoneNum: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void>;

  resetPasswordWithEmail(
    email: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void>;

  resetPasswordWithToken(
    pass: string,
    token: string,
    isPartnerAdminExpected?: boolean,
  ): Promise<void>;

  readPasswordRule(): IRequestPromise<SystemPasswordRule>;
}
