import { verifyToken } from '../src/biz/auth';
import { MxUser } from '../src/user/mxUserImpl';

// verifyToken()
//   .then((res) => {
//     console.debug('success', res);
//   })
//   .catch((e) => {
//     console.error(e);
//   });
MxUser.login({
  email: '<EMAIL>',
  isSuperAdmin: false,
  pass: 'P@ss123',
  rememberMe: false,
})
  .then((user) => {
    console.debug('success', user.id);
  })
  .catch((e) => {
    console.error(e);
  });
