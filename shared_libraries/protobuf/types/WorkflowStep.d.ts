import {WorkflowStepStatus} from './WorkflowStepStatus'
import {BoardActor} from './BoardActor'
import {WorkflowStepType} from './WorkflowStepType'
import {BoardReference} from './BoardReference'
import {WorkflowOutgoing} from './WorkflowOutgoing'
import {WorkflowCondition} from './WorkflowCondition'
import {WorkflowCheckpoint} from './WorkflowCheckpoint'
export interface WorkflowStep {
  name?: string
  description?: string
  order_number?: string
  is_parallel_with_prev_step?: boolean
  status?: WorkflowStepStatus
  enable_preparation?: boolean
  editor?: BoardActor
  hide_before_started?: boolean
  type?: WorkflowStepType
  board_id?: string
  board_view_token?: string
  input?: BoardReference
  output?: BoardReference
  outgoings?: WorkflowOutgoing[]
  outbound_data?: string
  inbound_data?: string
  parent_step?: string
  is_optional?: boolean
  preconditions?: WorkflowCondition[]
  condition?: WorkflowCondition
  checkpoints?: WorkflowCheckpoint[]
  is_holding?: boolean
  custom_data?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
