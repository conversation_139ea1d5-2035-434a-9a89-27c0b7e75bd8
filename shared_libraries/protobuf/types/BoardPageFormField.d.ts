import {FormFieldType} from './FormFieldType'
export interface BoardPageFormField {
  type?: FormFieldType
  name?: string
  x?: number
  y?: number
  width?: number
  height?: number
  flags?: number
  is_multiple_line?: boolean
  is_password?: boolean
  default_value?: string
  is_multiple_select?: boolean
  choices?: string[]
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
