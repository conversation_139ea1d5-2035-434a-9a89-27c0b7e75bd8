import {ClientRequestType} from './ClientRequestType'
import {<PERSON>lientParam} from './ClientParam'
import {CacheObject} from './CacheObject'
import {CacheMessage} from './CacheMessage'
import {UserActivityLog} from './UserActivityLog'
export interface ClientRequest {
  type?: ClientRequestType
  original_type?: ClientRequestType
  sequence?: string
  params?: ClientParam[]
  object?: CacheObject
  request_body?: string
  request_body_file_path?: string
  request_body_content_type?: string
  message?: CacheMessage
  user_activity?: UserActivityLog
  note?: string
}
