import {User} from './User'
import {Group} from './Group'
import {BoardUserStatus} from './BoardUserStatus'
import {BoardAccessType} from './BoardAccessType'
import {NotificationLevel} from './NotificationLevel'
import {BoardUserAOSM} from './BoardUserAOSM'
import {RequestingUserStatus} from './RequestingUserStatus'
import {BoardActor} from './BoardActor'
export interface BoardUser {
  user?: User
  group?: Group
  status?: BoardUserStatus
  type?: BoardAccessType
  is_notification_off?: boolean
  push_notification_level?: NotificationLevel
  type_indication_timestamp?: number
  is_alternative_host?: boolean
  is_owner_delegate?: boolean
  accessed_time?: number
  first_unread_feed_timestamp?: number
  invited_time?: number
  is_invited_in_session?: boolean
  action?: BoardUserAOSM
  is_from_team?: boolean
  teams?: number[]
  participant_number?: number
  requesting_user_status?: RequestingUserStatus
  responder?: BoardActor
  invite_msg?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  keep_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
