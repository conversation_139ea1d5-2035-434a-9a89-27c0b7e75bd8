import {UserGroup} from './UserGroup'
import {GroupUser} from './GroupUser'
import {Board} from './Board'
export interface RoutingChannel {
  name?: string
  picture?: number
  description?: string
  order_number?: string
  teams?: UserGroup[]
  include_all_admins?: boolean
  user?: GroupUser
  board?: Board
  unassigned_client_only?: boolean
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  keep_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
