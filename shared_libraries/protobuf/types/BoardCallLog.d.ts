import {BoardActor} from './BoardActor'
import {BoardCallStatus} from './BoardCallStatus'
import {CallStatus} from './CallStatus'
export interface BoardCallLog {
  from?: BoardActor
  to?: BoardActor
  start_time?: number
  end_time?: number
  status?: BoardCallStatus
  call_status?: CallStatus
  creator?: BoardActor
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
