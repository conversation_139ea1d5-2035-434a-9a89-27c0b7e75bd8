import {TelephoneNumber} from './TelephoneNumber'
import {TelephonyDomainSmsProvider} from './TelephonyDomainSmsProvider'
import {TelephonyDomainPartner} from './TelephonyDomainPartner'
export interface TelephonyDomain {
  id?: string
  name?: string
  description?: string
  numbers?: TelephoneNumber[]
  sms_provider?: TelephonyDomainSmsProvider
  partner?: TelephonyDomainPartner
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
