import {SocialType} from './SocialType'
import {BoardType} from './BoardType'
import {BoardRoutingStatus} from './BoardRoutingStatus'
import {WaitingRoomAudience} from './WaitingRoomAudience'
import {BoardUser} from './BoardUser'
import {BoardPage} from './BoardPage'
import {BoardResource} from './BoardResource'
import {BoardTag} from './BoardTag'
import {BoardPageGroup} from './BoardPageGroup'
import {BoardViewToken} from './BoardViewToken'
import {BoardFolder} from './BoardFolder'
import {BoardReferenceLink} from './BoardReferenceLink'
import {BoardUserRSVP} from './BoardUserRSVP'
import {BoardSession} from './BoardSession'
import {BoardCallLog} from './BoardCallLog'
import {BoardComment} from './BoardComment'
import {BoardTodo} from './BoardTodo'
import {BoardSignature} from './BoardSignature'
import {BoardTransaction} from './BoardTransaction'
import {BoardWorkflow} from './BoardWorkflow'
import {ObjectFeed} from './ObjectFeed'
import {ActionUserRoster} from './ActionUserRoster'
import {BoardUserActivity} from './BoardUserActivity'
import {BoardEditorType} from './BoardEditorType'
import {BoardPin} from './BoardPin'
import {BoardReminder} from './BoardReminder'
import {BoardMemberNotificationSetting} from './BoardMemberNotificationSetting'
import {ActionNotificationSetting} from './ActionNotificationSetting'
import {BoardNotificationSetting} from './BoardNotificationSetting'
import {BoardProperty} from './BoardProperty'
import {DueTimeFrameType} from './DueTimeFrameType'
import {BoardBroadcast} from './BoardBroadcast'
export interface Board {
  id?: string
  client_uuid?: string
  islive?: boolean
  isconversation?: boolean
  isdefault?: boolean
  isnote?: boolean
  istemp?: boolean
  is_restricted?: boolean
  is_team?: boolean
  use_member_name_as_name?: boolean
  use_member_avatar_as_cover?: boolean
  is_relation?: boolean
  is_transaction?: boolean
  is_inactive?: boolean
  inactive_time?: number
  is_bot_relation?: boolean
  is_duplicate?: boolean
  is_file?: boolean
  is_todo?: boolean
  is_signature?: boolean
  is_flexible?: boolean
  is_external?: boolean
  iscall?: boolean
  is_inbox?: boolean
  is_personal_room?: boolean
  milliseconds_personal_room_waiting_timeout?: number
  is_app_subscription?: boolean
  is_shadow_flow?: boolean
  is_owner_delegate_enabled?: boolean
  social_type?: SocialType
  is_client_editing_enabled?: boolean
  name?: string
  description?: string
  type?: BoardType
  workspace_id?: string
  invite_code?: string
  thumbnail_need_migrate?: string
  thumbnail?: number
  thumbnail_view_token?: string
  banner?: number
  banner_mobile?: number
  board_pdf?: number
  board_ppt?: number
  board_recording?: number
  thumbnail_source_page?: number
  thumbnail_source_resource?: number
  email_address?: string
  phone_number?: string
  is_acd?: boolean
  is_service_request?: boolean
  routing_status?: BoardRoutingStatus
  routing_channel?: number
  is_channel_subscription?: boolean
  is_content_library?: boolean
  is_client_resources?: boolean
  enable_waiting_room?: boolean
  waiting_room_audience?: WaitingRoomAudience
  teams?: BoardUser[]
  pages?: BoardPage[]
  resources?: BoardResource[]
  tags?: BoardTag[]
  page_groups?: BoardPageGroup[]
  view_tokens?: BoardViewToken[]
  folders?: BoardFolder[]
  reference_links?: BoardReferenceLink[]
  users?: BoardUser[]
  owner?: number
  user_rsvps?: BoardUserRSVP[]
  sessions?: BoardSession[]
  calls?: BoardCallLog[]
  comments?: BoardComment[]
  todos?: BoardTodo[]
  signatures?: BoardSignature[]
  transactions?: BoardTransaction[]
  workflows?: BoardWorkflow[]
  feeds?: ObjectFeed[]
  waiting_users?: ActionUserRoster[]
  user_activities?: BoardUserActivity[]
  user_activities_last?: number
  pin_editor_type?: BoardEditorType
  pins?: BoardPin[]
  reminders?: BoardReminder[]
  requesting_users?: BoardUser[]
  total_pages?: number
  total_members?: number
  total_comments?: number
  total_todos?: number
  total_open_todos?: number
  has_folder?: boolean
  total_signatures?: number
  total_emails?: number
  total_hits?: number
  total_creators?: number
  total_transactions?: number
  total_pins?: number
  total_open_signatures?: number
  total_open_transactions?: number
  total_size?: number
  access_control_board_id?: string
  board_member_notification_settings?: BoardMemberNotificationSetting
  action_notification_settings?: ActionNotificationSetting
  board_notification_settings?: BoardNotificationSetting
  archive_after?: number
  properties?: BoardProperty[]
  previous_due_date?: number
  due_date?: number
  due_in_timeframe?: DueTimeFrameType
  exclude_weekends?: boolean
  original_board_id?: string
  broadcasts?: BoardBroadcast[]
  revision?: number
  is_deleted?: boolean
  assignments?: string
  local_revision?: number
  index_local_field_after_revision?: number
  index_revision?: number
  index_version?: number
  revision_in_previous_update_member_job?: number
  created_time?: number
  updated_time?: number
}
