import {RichTextFormat} from './RichTextFormat'
import {DueTimeFrameType} from './DueTimeFrameType'
import {TransactionStatus} from './TransactionStatus'
import {DetailStatusCode} from './DetailStatusCode'
import {TransactionType} from './TransactionType'
import {BoardActor} from './BoardActor'
import {TransactionStep} from './TransactionStep'
import {TransactionStepGroup} from './TransactionStepGroup'
import {BoardReference} from './BoardReference'
import {BoardViewToken} from './BoardViewToken'
import {BoardResource} from './BoardResource'
import {BoardReminder} from './BoardReminder'
import {TransactionElement} from './TransactionElement'
export interface BoardTransaction {
  id?: string
  title?: string
  sub_title?: string
  content?: string
  content_format?: RichTextFormat
  display_status?: string
  is_active?: boolean
  expiration_date?: number
  is_expired?: boolean
  due_in_timeframe?: DueTimeFrameType
  exclude_weekends?: boolean
  status?: TransactionStatus
  detail_status?: DetailStatusCode
  callback_url?: string
  is_template?: boolean
  template_name?: string
  template_description?: string
  original_client_uuid?: string
  type?: TransactionType
  creator?: BoardActor
  enable_preparation?: boolean
  editor?: BoardActor
  enable_decline?: boolean
  steps?: TransactionStep[]
  step_groups?: TransactionStepGroup[]
  step_timeout?: number
  references?: BoardReference[]
  view_tokens?: BoardViewToken[]
  card?: string
  card_description?: string
  custom_data?: string
  sub_type?: string
  resources?: BoardResource[]
  reminders?: BoardReminder[]
  custom_result?: string
  segments?: TransactionElement[]
  pin?: number
  total_used_count?: number
  last_used_timestamp?: number
  last_modified_time?: number
  workflow?: number
  step?: number
  is_workflow_source?: boolean
  original?: number
  original_masked?: number
  original_csv?: number
  original_csv_masked?: number
  has_custom_folder?: boolean
  custom_folder_name?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
