import {User} from './User'
import {GroupUserStatus} from './GroupUserStatus'
import {GroupAccessType} from './GroupAccessType'
export interface GroupUser {
  user?: User
  status?: GroupUserStatus
  type?: GroupAccessType
  onboarded_time?: number
  alias?: string
  is_invalid?: boolean
  message?: string
  invalid_fields?: string[]
  order_number?: string
  role?: number
  roles?: number[]
  role_assigned_time?: number
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  keep_deleted?: boolean
  created_time?: number
  updated_time?: number
}
