import {User} from './User'
import {BoardActor} from './BoardActor'
import {BoardTag} from './BoardTag'
export interface BoardPageElement {
  svg_tag?: string
  highlight?: boolean
  readonly?: boolean
  resource?: number
  resource_path?: string
  resource_name?: string
  creator_sequence?: number
  OBSOLETE_user?: User
  creator?: BoardActor
  tags?: BoardTag[]
  assignments?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
