import {User} from './User'
import {BoardViewToken} from './BoardViewToken'
import {ActionUserRoster} from './ActionUserRoster'
import {ObjectFeedType} from './ObjectFeedType'
import {Board} from './Board'
import {ObjectFeedStatus} from './ObjectFeedStatus'
import {ObjectFeedViaSource} from './ObjectFeedViaSource'
import {FeedReaction} from './FeedReaction'
export interface ObjectFeed {
  actor?: User
  view_token?: BoardViewToken
  roster?: ActionUserRoster
  type?: ObjectFeedType
  board?: Board
  timestamp?: number
  is_pinned?: boolean
  delegate?: User
  status?: ObjectFeedStatus
  via?: ObjectFeedViaSource
  reactions?: FeedReaction[]
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
