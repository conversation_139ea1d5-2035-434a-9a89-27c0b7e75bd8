import {AudioServerCore} from './AudioServerCore'
import {AudioEdgeServer} from './AudioEdgeServer'
export interface AudioServer {
  availability_zone?: string
  server_url?: string
  server_addr?: string
  server_name?: string
  port?: number
  tcp_port?: number
  capacity?: number
  keepalive_timestamp?: number
  cores?: AudioServerCore[]
  edge_servers?: AudioEdgeServer[]
  recording_server_addr?: string
  internal_server_addr?: string
  fingerprint?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
