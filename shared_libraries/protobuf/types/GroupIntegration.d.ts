import {GroupIntegrationType} from './GroupIntegrationType'
import {WebApp} from './WebApp'
import {GroupUser} from './GroupUser'
import {Group} from './Group'
import {GroupBoard} from './GroupBoard'
import {SamlIdentityProviderConfig} from './SamlIdentityProviderConfig'
import {GroupUserRoleType} from './GroupUserRoleType'
import {GroupEmailConfig} from './GroupEmailConfig'
export interface GroupIntegration {
  type?: GroupIntegrationType
  enable_auto_provision?: boolean
  webapp?: WebApp
  user?: GroupUser
  group?: Group
  board?: GroupBoard
  is_free_subscription?: boolean
  category?: string
  idp_conf?: SamlIdentityProviderConfig
  saml_user_role_types?: GroupUserRoleType[]
  domain?: string
  email_config?: GroupEmailConfig
  connector?: string
  crm_configuration?: string
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  keep_deleted?: boolean
  created_time?: number
  updated_time?: number
}
