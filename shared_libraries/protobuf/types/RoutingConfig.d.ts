import {RoutingChannel} from './RoutingChannel'
import {RoutingWeekday} from './RoutingWeekday'
import {RoutingSpecialDay} from './RoutingSpecialDay'
import {UserTag} from './UserTag'
export interface RoutingConfig {
  acd_max_conns_per_agent?: number
  acd_channels?: RoutingChannel[]
  sr_channels?: RoutingChannel[]
  weekdays?: RoutingWeekday[]
  special_days?: RoutingSpecialDay[]
  acd_connection_timeout?: number
  template_messages?: UserTag[]
  template_messages_sr?: UserTag[]
  prompt_leave_message?: string
  disable_acd_leave_message?: boolean
  sequence?: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
