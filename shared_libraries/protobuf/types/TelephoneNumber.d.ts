import {UserResource} from './UserResource'
export interface TelephoneNumber {
  number?: string
  location?: string
  plain_number?: string
  prompt_meetid?: number
  prompt_participantnumber?: number
  prompt_joined?: number
  prompt_left?: number
  prompt_ended?: number
  prompt_retrymeetid?: number
  prompt_invalidmeetid?: number
  prompt_notinprogress?: number
  prompt_noinput?: number
  prompt_goodbye?: number
  prompt_waiting?: number
  prompt_decline?: number
  prompt_muted?: number
  prompt_unmuted?: number
  prompt_mute_instruction?: number
  prompt_password?: number
  prompt_password_invalid?: number
  prompt_password_try_later?: number
  prompt_recording?: number
  prompt_first_attendee?: number
  resources?: UserResource[]
  is_default?: boolean
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
  assignments?: string
}
