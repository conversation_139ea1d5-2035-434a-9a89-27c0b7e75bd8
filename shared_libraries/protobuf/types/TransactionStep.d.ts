import {BoardA<PERSON>} from './BoardActor'
import {TransactionActionStyle} from './TransactionActionStyle'
import {TransactionActionLog} from './TransactionActionLog'
import {TransactionStepType} from './TransactionStepType'
import {TransactionStepStatus} from './TransactionStepStatus'
import {TransactionElement} from './TransactionElement'
export interface TransactionStep {
  id?: string
  assignee?: BoardActor
  action_style?: TransactionActionStyle
  actions?: string
  action_logs?: TransactionActionLog[]
  order_number?: string
  step_group?: string
  type?: TransactionStepType
  status?: TransactionStepStatus
  viewed_time?: number
  custom_data?: string
  contents?: TransactionElement[]
  sequence?: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
