import {PartnerType} from './PartnerType'
import {PartnerContact} from './PartnerContact'
import {PartnerCap} from './PartnerCap'
import {PartnerUser} from './PartnerUser'
import {PartnerIntegration} from './PartnerIntegration'
import {PartnerPlanCode} from './PartnerPlanCode'
import {PartnerGroup} from './PartnerGroup'
import {PartnerWebApp} from './PartnerWebApp'
import {PartnerTelephonyDomain} from './PartnerTelephonyDomain'
export interface Partner {
  id?: string
  name?: string
  type?: PartnerType
  description?: string
  configuration?: string
  contact?: PartnerContact
  partner_caps?: PartnerCap
  allow_trial?: boolean
  max_trial_days?: number
  upgrade_info?: string
  shared_content_library_group_id?: string
  members?: PartnerUser[]
  integrations?: PartnerIntegration[]
  plan_codes?: PartnerPlanCode[]
  default_plan_code?: string
  default_plan_code_local?: string
  group_templates?: PartnerGroup[]
  webapps?: PartnerWebApp[]
  groups?: PartnerGroup[]
  partner_telephony_domains?: PartnerTelephonyDomain[]
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
