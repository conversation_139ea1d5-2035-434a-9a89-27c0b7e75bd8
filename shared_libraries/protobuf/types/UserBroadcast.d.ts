import {UserContact} from './UserContact'
export interface UserBroadcast {
  title?: string
  contacts?: UserContact[]
  msg_count?: number
  msg_sent_timestamp?: number
  last_modified_timestamp?: number
  is_default?: boolean
  sequence?: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  local_revision?: number
  created_time?: number
  updated_time?: number
}
