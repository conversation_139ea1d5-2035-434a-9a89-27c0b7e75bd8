{"name": "@moxo/shared", "version": "0.0.1", "main": "lib/index.js", "author": "colin", "license": "MIT", "private": true, "type": "module", "files": ["lib"], "scripts": {"precommit": "lint-staged", "build": "rm -rf lib/ && tsc -p tsconfig.json", "dev": "tsc -p tsconfig.json --watch --preserveWatchOutput --incremental", "lint": "eslint --ext .ts src", "lint:report": "pnpm lint --format json --output-file report.json", "prepack": "pnpm build", "test": "vitest src", "test:ci": "pnpm run test --silent --coverage"}, "devDependencies": {"@moxo/tsconfig": "workspace:*"}, "dependencies": {"uuid": "^11.1.0", "type-fest": "^4.41.0", "@moxo/types": "workspace:*"}}