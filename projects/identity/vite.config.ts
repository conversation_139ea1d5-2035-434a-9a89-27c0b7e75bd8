import path from 'node:path';
import process from 'node:process';
import { loadEnv } from 'vite';
import type { ConfigEnv, UserConfig } from 'vite';
import { createVitePlugins } from './build/vite';
import { exclude, include } from './build/vite/optimize';
import fs from 'fs';
function getProxyUrl() {
  try {
    const envPath = path.resolve(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf-8');
    const env = {};
    envContent.split('\n').forEach((line) => {
      const [key, value] = line.split('=');
      if (key && value) {
        env[key.trim()] = value.trim();
      }
    });
    return env.proxyUrl || 'localhost:8080';
  } catch (error) {
    console.warn('Failed to read .env file, using default proxy URL');
    return 'localhost:8080';
  }
}
export default ({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);

  return {
    base: env.VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(mode),

    server: {
      host: true,
      port: 3000,
      proxy: {
        // HTTP 代理配置
        '^/(board|group|webapp|client|v1|user|workflow|integration|mepx/v1|ws|w)': {
          target: `https://${getProxyUrl()}`,
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            // 动态更新目标地址
            proxy.on('proxyReq', (proxyReq, req, res) => {
              const proxyUrl = getProxyUrl();
              proxyReq.setHeader('host', proxyUrl);
              proxyReq.setHeader('Accept-Encoding', '');
            });
          },
        },
        // WebSocket 代理配置 - 单独配置 WebSocket
        '^/ws': {
          target: `wss://${getProxyUrl()}`,
          ws: true,
          changeOrigin: true,
          secure: true,
        },
      },
    },

    resolve: {
      alias: {
        '@': path.join(__dirname, './src'),
        '~': path.join(__dirname, './src/assets'),
        '~root': path.join(__dirname, '.'),
      },
    },

    build: {
      cssCodeSplit: false,
      chunkSizeWarningLimit: 2048,
      outDir: env.VITE_APP_OUT_DIR || 'dist',
    },

    optimizeDeps: { include, exclude },
  };
};
