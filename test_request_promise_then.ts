import { RequestPromise } from './shared_libraries/sdk/src/network/ajax';

// 测试 RequestPromise 的 then 方法是否正确返回 RequestPromise 对象并复制 requestId
function testRequestPromiseThen() {
  console.log('开始测试 RequestPromise.then 方法...');

  // 创建一个测试用的 RequestPromise
  const originalRequestId = 'test-request-id-123';
  const originalPromise = new RequestPromise<string>((resolve) => {
    setTimeout(() => resolve('原始结果'), 100);
  }, originalRequestId);

  console.log('原始 RequestPromise requestId:', originalPromise.requestId);

  // 测试 then 方法返回的对象类型和 requestId
  const chainedPromise = originalPromise.then((result) => {
    console.log('第一个 then 回调执行，结果:', result);
    return '链式调用结果';
  });

  console.log('链式调用后的对象是否为 RequestPromise:', chainedPromise instanceof RequestPromise);
  console.log('链式调用后的 requestId:', chainedPromise.requestId);
  console.log('requestId 是否保持一致:', chainedPromise.requestId === originalRequestId);

  // 测试多级链式调用
  const secondChainedPromise = chainedPromise.then((result) => {
    console.log('第二个 then 回调执行，结果:', result);
    return '第二级链式调用结果';
  });

  console.log('第二级链式调用后的对象是否为 RequestPromise:', secondChainedPromise instanceof RequestPromise);
  console.log('第二级链式调用后的 requestId:', secondChainedPromise.requestId);
  console.log('第二级 requestId 是否保持一致:', secondChainedPromise.requestId === originalRequestId);

  // 执行最终的结果验证
  secondChainedPromise.then((finalResult) => {
    console.log('最终结果:', finalResult);
    console.log('测试完成！');
  }).catch((error) => {
    console.error('测试出错:', error);
  });
}

// 运行测试
testRequestPromiseThen();
